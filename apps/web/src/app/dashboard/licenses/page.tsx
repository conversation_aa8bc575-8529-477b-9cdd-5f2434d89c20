"use client";

import { useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { PaginationWithInfo } from "@/components/ui/pagination";
import { useLicenses } from "@/hooks/use-license-management";

export default function LicensesPage() {
	const router = useRouter();
	const searchParams = useSearchParams();

	// Get pagination and filter params from URL
	const page = Number.parseInt(searchParams.get("page") || "1");
	const limit = Number.parseInt(searchParams.get("limit") || "20");
	const licenseType = searchParams.get("licenseType") || "";
	const isActive = searchParams.get("isActive");
	const isExpired = searchParams.get("isExpired");
	const search = searchParams.get("search") || "";
	const email = searchParams.get("email") || "";

	// Local state for filters
	const [filters, setFilters] = useState({
		licenseType,
		isActive:
			isActive === "true" ? true : isActive === "false" ? false : undefined,
		isExpired:
			isExpired === "true" ? true : isExpired === "false" ? false : undefined,
		search,
		email,
	});

	// Fetch licenses with current filters
	const {
		data: licensesData,
		isLoading,
		error,
	} = useLicenses({
		page,
		limit,
		...filters,
	});

	// Update URL with new parameters
	const updateUrl = (
		newParams: Record<string, string | number | boolean | undefined>,
	) => {
		const params = new URLSearchParams(searchParams);

		Object.entries(newParams).forEach(([key, value]) => {
			if (value !== undefined && value !== "" && value !== null) {
				params.set(key, String(value));
			} else {
				params.delete(key);
			}
		});

		router.push(`/dashboard/licenses?${params.toString()}`);
	};

	// Handle page change
	const handlePageChange = (newPage: number) => {
		updateUrl({ page: newPage });
	};

	// Handle filter changes
	const handleFilterChange = (
		key: string,
		value: string | boolean | undefined,
	) => {
		const newFilters = { ...filters, [key]: value };
		setFilters(newFilters);
		updateUrl({ ...newFilters, page: 1 }); // Reset to page 1 when filtering
	};

	// Handle search
	const handleSearch = () => {
		updateUrl({ ...filters, page: 1 });
	};

	// Clear all filters
	const clearFilters = () => {
		const clearedFilters = {
			licenseType: "",
			isActive: undefined,
			isExpired: undefined,
			search: "",
			email: "",
		};
		setFilters(clearedFilters);
		updateUrl({ ...clearedFilters, page: 1 });
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString();
	};

	const getLicenseTypeBadge = (type: string) => {
		const variants = {
			trial: "secondary",
			standard: "default",
			extended: "destructive",
		} as const;

		return (
			<Badge variant={variants[type as keyof typeof variants] || "outline"}>
				{type.charAt(0).toUpperCase() + type.slice(1)}
			</Badge>
		);
	};

	const getStatusBadge = (isActive: boolean, isExpired: boolean) => {
		if (!isActive) {
			return <Badge variant="destructive">Refunded</Badge>;
		}
		if (isExpired) {
			return <Badge variant="secondary">Expired</Badge>;
		}
		return <Badge variant="default">Active</Badge>;
	};

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="text-3xl font-bold">License Management</h1>
			</div>

			{/* Filters */}
			<Card>
				<CardHeader>
					<CardTitle>Filters</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="space-y-2">
							<Label htmlFor="search">Search</Label>
							<Input
								id="search"
								placeholder="Email or license key..."
								value={filters.search}
								onChange={(e) =>
									setFilters({ ...filters, search: e.target.value })
								}
								onKeyDown={(e) => e.key === "Enter" && handleSearch()}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="email">Email</Label>
							<Input
								id="email"
								placeholder="Filter by email..."
								value={filters.email}
								onChange={(e) =>
									setFilters({ ...filters, email: e.target.value })
								}
								onKeyDown={(e) => e.key === "Enter" && handleSearch()}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="licenseType">License Type</Label>
							<Select
								value={filters.licenseType}
								onValueChange={(value) =>
									handleFilterChange("licenseType", value)
								}
							>
								<SelectTrigger>
									<SelectValue placeholder="All types" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="">All types</SelectItem>
									<SelectItem value="trial">Trial</SelectItem>
									<SelectItem value="standard">Standard</SelectItem>
									<SelectItem value="extended">Extended</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div className="space-y-2">
							<Label htmlFor="status">Status</Label>
							<Select
								value={
									filters.isActive === true
										? "active"
										: filters.isActive === false
											? "refunded"
											: filters.isExpired === true
												? "expired"
												: ""
								}
								onValueChange={(value) => {
									if (value === "active") {
										handleFilterChange("isActive", true);
										handleFilterChange("isExpired", undefined);
									} else if (value === "refunded") {
										handleFilterChange("isActive", false);
										handleFilterChange("isExpired", undefined);
									} else if (value === "expired") {
										handleFilterChange("isActive", undefined);
										handleFilterChange("isExpired", true);
									} else {
										handleFilterChange("isActive", undefined);
										handleFilterChange("isExpired", undefined);
									}
								}}
							>
								<SelectTrigger>
									<SelectValue placeholder="All statuses" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="">All statuses</SelectItem>
									<SelectItem value="active">Active</SelectItem>
									<SelectItem value="expired">Expired</SelectItem>
									<SelectItem value="refunded">Refunded</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>

					<div className="flex gap-2">
						<Button onClick={handleSearch}>Search</Button>
						<Button variant="outline" onClick={clearFilters}>
							Clear Filters
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Results */}
			<Card>
				<CardHeader>
					<CardTitle>Licenses</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<div className="text-muted-foreground">Loading licenses...</div>
						</div>
					) : error ? (
						<div className="flex items-center justify-center py-8">
							<div className="text-destructive">Failed to load licenses</div>
						</div>
					) : !licensesData?.licenses.length ? (
						<div className="flex items-center justify-center py-8">
							<div className="text-muted-foreground">No licenses found</div>
						</div>
					) : (
						<>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>License Key</TableHead>
										<TableHead>Email</TableHead>
										<TableHead>Type</TableHead>
										<TableHead>Status</TableHead>
										<TableHead>Devices</TableHead>
										<TableHead>Created</TableHead>
										<TableHead>Expires</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{licensesData.licenses.map((license) => (
										<TableRow key={license.id}>
											<TableCell className="font-mono text-sm">
												{license.licenseKey}
											</TableCell>
											<TableCell>{license.email}</TableCell>
											<TableCell>
												{getLicenseTypeBadge(license.licenseType)}
											</TableCell>
											<TableCell>
												{getStatusBadge(license.isActive, license.isExpired)}
											</TableCell>
											<TableCell>
												{license.devicesUsed} / {license.maxDevices}
											</TableCell>
											<TableCell>{formatDate(license.createdAt)}</TableCell>
											<TableCell>
												{license.expiresAt
													? formatDate(license.expiresAt)
													: "Never"}
											</TableCell>
										</TableRow>
									))}
								</TableBody>
							</Table>

							{/* Pagination */}
							{licensesData.pagination.totalPages > 1 && (
								<div className="mt-6">
									<PaginationWithInfo
										currentPage={licensesData.pagination.page}
										totalPages={licensesData.pagination.totalPages}
										totalCount={licensesData.pagination.totalCount}
										itemsPerPage={licensesData.pagination.limit}
										onPageChange={handlePageChange}
									/>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
