{"info": {"_postman_id": "8a9b32cd-04ce-4138-a422-5aa313bc7f51", "name": "Snapback Server API", "description": "Complete API collection for Snapback Server with license management and payment processing endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "32877280"}, "item": [{"name": "Authentication", "item": [{"name": "Sign Up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"<PERSON>\",\n  \"password\": \"Testing1234\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/sign-up/email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "sign-up", "email"]}}, "response": []}, {"name": "Sign In", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testsetstet\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/auth/sign-in/email", "host": ["{{baseUrl}}"], "path": ["api", "auth", "sign-in", "email"]}}, "response": []}]}, {"name": "User Management", "description": "RBAC (Role-Based Access Control) endpoints for user management. Requires authentication via better-auth session cookies.", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}, "description": "Get current authenticated user information and permissions. Requires valid session authentication."}, "response": []}, {"name": "List Users", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/users?page=1&limit=20&role=USER&isActive=true&search=john", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination (default: 1)"}, {"key": "limit", "value": "20", "description": "Number of users per page (default: 20, max: 100)"}, {"key": "role", "value": "USER", "description": "Filter by user role: <PERSON>E<PERSON><PERSON>, USER, MANAGER, ADMIN, SUPER_ADMIN"}, {"key": "isActive", "value": "true", "description": "Filter by active status: true or false"}, {"key": "search", "value": "john", "description": "Search users by name or email"}]}, "description": "List users with pagination and filtering. Requires MANAGER role or higher."}, "response": []}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePassword123!\",\n  \"role\": \"USER\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}, "description": "Create a new user account. Requires ADMIN role or higher. Available roles: <PERSON><PERSON><PERSON><PERSON>, USER, MANAGER, ADMIN, SUPER_ADMIN."}, "response": []}, {"name": "Update User", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"MANAGER\",\n  \"isActive\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "{{userId}}"]}, "description": "Update user information. Requires MANAGER role or higher. Users can only manage users with lower roles than their own."}, "response": []}, {"name": "Send User Invitation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"role\": \"USER\",\n  \"message\": \"Welcome to our team! Please accept this invitation to join our platform.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/users/invite", "host": ["{{baseUrl}}"], "path": ["api", "users", "invite"]}, "description": "Send an invitation to a new user. Requires MANAGER role or higher. The invited user will receive an email with instructions to set up their account."}, "response": []}]}, {"name": "License Management", "item": [{"name": "List Licenses", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses?page=1&limit=20&licenseType=standard&isActive=true&search=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["api", "licenses"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination (default: 1)"}, {"key": "limit", "value": "20", "description": "Number of results per page (default: 20, max: 100)"}, {"key": "licenseType", "value": "standard", "description": "Filter by license type (trial, standard, extended)"}, {"key": "isActive", "value": "true", "description": "Filter by active status (true/false)"}, {"key": "isExpired", "value": "false", "description": "Filter by expiration status (true/false)", "disabled": true}, {"key": "search", "value": "<EMAIL>", "description": "Search by email or license key"}, {"key": "email", "value": "<EMAIL>", "description": "Filter by specific email", "disabled": true}]}, "description": "List all licenses with pagination and filtering options. Requires ADMIN role or higher."}, "response": []}, {"name": "Create Trial License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"trial\",\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"John's MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 2023\",\n    \"operatingSystem\": \"macOS 14.1\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"16 GB\",\n    \"userNickname\": \"Work Laptop\",\n    \"location\": \"Office\",\n    \"notes\": \"Primary development machine\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Create Standard License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"standard\",\n  \"deviceId\": \"b2c3d4e5f67890********************abcdef********************1234a\",\n  \"stripePaymentIntentId\": \"pi_**********abcdef\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Sarah's iMac\",\n    \"deviceType\": \"iMac\",\n    \"deviceModel\": \"24-inch M1 2021\",\n    \"operatingSystem\": \"macOS 13.6\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"4480x2520\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Design Station\",\n    \"location\": \"Home Office\",\n    \"notes\": \"Main design workstation\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Create Extended License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"licenseType\": \"extended\",\n  \"deviceId\": \"c3d4e5f67890********************abcdef********************1234ab\",\n  \"stripePaymentIntentId\": \"pi_abcdef**********\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Team Mac Studio\",\n    \"deviceType\": \"Mac Studio\",\n    \"deviceModel\": \"M2 Ultra 2023\",\n    \"operatingSystem\": \"macOS 14.2\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"6016x3384\",\n    \"totalMemory\": \"128 GB\",\n    \"userNickname\": \"Render Farm\",\n    \"location\": \"Studio\",\n    \"notes\": \"High-performance rendering machine\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/create", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "create"]}}, "response": []}, {"name": "Validate License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"appVersion\": \"1.2.3\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"14-inch M2 Pro 2023\",\n    \"operatingSystem\": \"macOS 14.3\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3024x1964\",\n    \"totalMemory\": \"32 GB\",\n    \"userNickname\": \"Updated Work Laptop\",\n    \"location\": \"Remote Office\",\n    \"notes\": \"Updated with more RAM\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/validate", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "validate"]}}, "response": []}, {"name": "Resend License Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/resend", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "resend"]}}, "response": []}, {"name": "Upgrade License", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 2,\n  \"stripePaymentIntentId\": \"pi_upgrade**********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/upgrade", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "upgrade"]}}, "response": []}, {"name": "Get License Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses/status/ABCD1234EFGH5678IJKL9012", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "status", "ABCD1234EFGH5678IJKL9012"]}}, "response": []}, {"name": "Remove <PERSON>ce", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{deviceToken}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/licenses/devices/a1b2c3d4e5f67890********************abcdef********************1234", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "a1b2c3d4e5f67890********************abcdef********************1234"]}}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{deviceToken}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"deviceId\": \"a1b2c3d4e5f67890********************abcdef********************1234\",\n  \"deviceMetadata\": {\n    \"deviceName\": \"Updated MacBook Pro Name\",\n    \"deviceType\": \"MacBook Pro\",\n    \"deviceModel\": \"16-inch M2 Max 2023\",\n    \"operatingSystem\": \"macOS 14.4\",\n    \"architecture\": \"arm64\",\n    \"screenResolution\": \"3456x2234\",\n    \"totalMemory\": \"64 GB\",\n    \"userNickname\": \"Beast Machine\",\n    \"location\": \"New Office\",\n    \"notes\": \"Upgraded to 16-inch model with more power\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/licenses/devices/metadata", "host": ["{{baseUrl}}"], "path": ["api", "licenses", "devices", "metadata"]}}, "response": []}]}, {"name": "Payment Processing", "item": [{"name": "Get Pricing Information", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/pricing", "host": ["{{baseUrl}}"], "path": ["api", "payments", "pricing"]}}, "response": []}, {"name": "Create Payment Intent (Standard)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}, "response": []}, {"name": "Create Payment Intent (Extended)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"extended\",\n  \"additionalDevices\": 2,\n  \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-payment-intent"]}}, "response": []}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseType\": \"standard\",\n  \"additionalDevices\": 0,\n  \"email\": \"<EMAIL>\",\n  \"deviceId\": \"d4e5f67890********************abcdef********************1234abc\",\n  \"successUrl\": \"https://yourapp.com/success\",\n  \"cancelUrl\": \"https://yourapp.com/cancel\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-checkout-session", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-checkout-session"]}}, "response": []}, {"name": "Create Upgrade Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"additionalDevices\": 3\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/create-upgrade-payment-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-upgrade-payment-intent"]}}, "response": []}, {"name": "Get Checkout Session Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/checkout-session/cs_test_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "checkout-session", "cs_test_**********abcdef"]}}, "response": []}, {"name": "Get Payment Intent Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/payments/payment-intent/pi_**********abcdef", "host": ["{{baseUrl}}"], "path": ["api", "payments", "payment-intent", "pi_**********abcdef"]}}, "response": []}, {"name": "Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "stripe-signature", "value": "t=**********,v1=signature_hash_here", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_**********abcdef\",\n  \"object\": \"event\",\n  \"type\": \"payment_intent.succeeded\",\n  \"data\": {\n    \"object\": {\n      \"id\": \"pi_**********abcdef\",\n      \"object\": \"payment_intent\",\n      \"status\": \"succeeded\",\n      \"amount\": 499,\n      \"currency\": \"usd\",\n      \"metadata\": {\n        \"licenseType\": \"standard\",\n        \"additionalDevices\": \"0\",\n        \"email\": \"<EMAIL>\",\n        \"deviceId\": \"\",\n        \"flow_type\": \"embedded\"\n      }\n    }\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/payments/webhook", "host": ["{{baseUrl}}"], "path": ["api", "payments", "webhook"]}}, "response": []}]}, {"name": "Refund Management", "item": [{"name": "Request Refund", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"reason\": \"Software doesn't work with my macOS version. Requesting full refund due to compatibility issues.\",\n  \"requestedBy\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/refunds/request", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "request"]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"reason\": \"Software doesn't work with my macOS version. Requesting full refund due to compatibility issues.\",\n  \"requestedBy\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/refunds/request", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "request"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Refund request submitted successfully\",\n  \"refundRequest\": {\n    \"id\": \"req_**********abcdef\",\n    \"status\": \"PENDING\",\n    \"reason\": \"Software doesn't work with my macOS version. Requesting full refund due to compatibility issues.\",\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\"\n  }\n}"}]}, {"name": "Process Refund (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"action\": \"approve\",\n  \"adminNotes\": \"Approved - valid compatibility concern\",\n  \"amount\": 499\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/refunds/process", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "process"]}}, "response": [{"name": "Approval Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"action\": \"approve\",\n  \"adminNotes\": \"Approved - valid compatibility concern\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/refunds/process", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "process"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Refund processed successfully\",\n  \"refund\": {\n    \"id\": \"re_**********abcdef\",\n    \"amount\": 499,\n    \"status\": \"PROCESSED\"\n  }\n}"}, {"name": "Rejection Success", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"action\": \"reject\",\n  \"adminNotes\": \"Rejected - refund request exceeds 30-day policy\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/refunds/process", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "process"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"message\": \"Refund request rejected\",\n  \"status\": \"REJECTED\"\n}"}]}, {"name": "Get Refund Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/refunds/status/ABCD1234EFGH5678IJKL9012", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "status", "ABCD1234EFGH5678IJKL9012"]}}, "response": [{"name": "Refunded License", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/refunds/status/ABCD1234EFGH5678IJKL9012", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "status", "ABCD1234EFGH5678IJKL9012"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n  \"refunded\": true,\n  \"refundedAt\": \"2024-01-16T14:30:00.000Z\",\n  \"refundReason\": \"Customer requested refund due to compatibility issues\",\n  \"refundAmount\": 499,\n  \"refundRequest\": {\n    \"id\": \"req_**********abcdef\",\n    \"status\": \"PROCESSED\",\n    \"reason\": \"Customer requested refund due to compatibility issues\",\n    \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n    \"processedAt\": \"2024-01-16T14:30:00.000Z\"\n  }\n}"}, {"name": "Non-Refunded License", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/refunds/status/EFGH5678IJKL9012ABCD1234", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "status", "EFGH5678IJKL9012ABCD1234"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"licenseKey\": \"EFGH5678IJKL9012ABCD1234\",\n  \"refunded\": false,\n  \"refundedAt\": null,\n  \"refundReason\": null,\n  \"refundAmount\": null,\n  \"refundRequest\": null\n}"}]}, {"name": "Get Refund History (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/refunds/history?page=1&limit=20&status=PENDING&search=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "history"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "limit", "value": "20", "description": "Number of results per page (max 100)"}, {"key": "status", "value": "PENDING", "description": "Filter by status: PENDING, APPROVED, REJECTED, PROCESSED, FAILED"}]}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/refunds/history?page=1&limit=20", "host": ["{{baseUrl}}"], "path": ["api", "refunds", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"refundRequests\": [\n    {\n      \"id\": \"req_**********abcdef\",\n      \"status\": \"PROCESSED\",\n      \"reason\": \"Customer requested refund due to compatibility issues\",\n      \"amount\": null,\n      \"requestedBy\": \"<EMAIL>\",\n      \"adminNotes\": \"Approved - valid compatibility concern\",\n      \"processedBy\": null,\n      \"createdAt\": \"2024-01-15T10:30:00.000Z\",\n      \"processedAt\": \"2024-01-16T14:30:00.000Z\",\n      \"license\": {\n        \"licenseKey\": \"ABCD1234EFGH5678IJKL9012\",\n        \"email\": \"<EMAIL>\",\n        \"licenseType\": \"standard\",\n        \"refundedAt\": \"2024-01-16T14:30:00.000Z\",\n        \"refundAmount\": 499\n      }\n    }\n  ],\n  \"pagination\": {\n    \"page\": 1,\n    \"limit\": 20,\n    \"totalCount\": 45,\n    \"totalPages\": 3,\n    \"hasNextPage\": true,\n    \"hasPreviousPage\": false\n  }\n}"}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "deviceToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example.token", "type": "string"}, {"key": "userId", "value": "user_**********abcdef", "type": "string", "description": "User ID for user management operations"}]}