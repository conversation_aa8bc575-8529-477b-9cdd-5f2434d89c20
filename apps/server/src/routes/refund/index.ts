import { listRefundsSchema, processRefundSchema, requestRefundSchema } from "@snapback/shared";
import { Router } from "express";
import type { Prisma } from "prisma/generated/client";
import z from "zod";
import {
	isRefundEligible,
	processFullRefund,
	processPartialRefund,
	type RefundOptions,
	updateLicenseAfterRefund,
} from "@/services/refund";
import {
	sendRefundRejectionEmail,
	sendRefundRequestConfirmationEmail,
} from "@/templates";
import {
	ErrorCode,
	handleDatabaseError,
	handleStripeError,
	handleZodError,
	type PrismaError,
	type StripeError,
	sendErrorResponse,
} from "@/utils/errors";
import {
	EndpointPrefix,
	extractRequestContext,
	Logger,
	requestLoggingMiddleware,
} from "@/utils/logger";
import { calculatePagination, extractPaginationFromQuery } from "@/utils/pagination";
import prisma from "../../../prisma";
import { logApiResponse } from "../license";

const router: Router = Router();

/**
 * @route POST /api/refunds/request
 * @description Request a refund for a license
 * @access Public
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.licenseKey - License key to refund
 * @param {string} req.body.reason - Reason for refund request
 * @param {string} req.body.requestedBy - Email of person requesting refund
 *
 * @returns {Object} 200 - Refund request created successfully
 * @returns {Object} 400 - Validation error or license not eligible
 * @returns {Object} 404 - License not found
 * @returns {Object} 409 - Refund request already exists
 * @returns {Object} 500 - Internal server error
 */
router.post(
	"/request",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_CREATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				"Processing refund request",
				context,
			);

			const { licenseKey, reason, requestedBy } = requestRefundSchema.parse(
				req.body,
			);

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`Refund requested for license: ${licenseKey}`,
				{
					...context,
					body: { licenseKey, reason, requestedBy },
				},
			);

			// Find the license
			const license = await prisma.license.findUnique({
				where: { licenseKey },
				include: { refundRequest: true },
			});

			if (!license) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`License not found: ${licenseKey}`,
					context,
				);
				return sendErrorResponse(
					res,
					"License not found",
					404,
					ErrorCode.LICENSE_NOT_FOUND,
					"The specified license key was not found",
				);
			}

			// Check if refund request already exists
			if (license.refundRequest) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`Refund request already exists for license: ${licenseKey}`,
					context,
				);
				return sendErrorResponse(
					res,
					"Refund request already exists",
					409,
					ErrorCode.ALREADY_EXISTS,
					"A refund request already exists for this license",
					{ status: license.refundRequest.status },
				);
			}

			// Check refund eligibility
			const eligibility = isRefundEligible(license);
			if (!eligibility.eligible) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`License not eligible for refund: ${eligibility.reason}`,
					context,
				);
				return sendErrorResponse(
					res,
					"License not eligible for refund",
					400,
					ErrorCode.VALIDATION_ERROR,
					eligibility.reason || "License is not eligible for refund",
				);
			}

			// Create refund request
			const refundRequest = await prisma.refundRequest.create({
				data: {
					licenseId: license.id,
					requestedBy,
					reason,
					status: "PENDING",
				},
			});

			// Create audit log entry
			await prisma.auditLog.create({
				data: {
					action: "REFUND_REQUESTED",
					licenseKey: license.licenseKey,
					details: {
						refundRequestId: refundRequest.id,
						reason,
						requestedBy,
					},
				},
			});

			// Send confirmation email
			try {
				await sendRefundRequestConfirmationEmail(
					requestedBy,
					license.licenseKey,
					reason,
					refundRequest.id,
				);

				Logger.info(
					EndpointPrefix.LICENSE_CREATE,
					`Refund request confirmation email sent: ${requestedBy}`,
					{ ...context, body: { refundRequestId: refundRequest.id } },
				);
			} catch (emailError) {
				Logger.error(
					EndpointPrefix.LICENSE_CREATE,
					`Failed to send refund request confirmation email: ${emailError instanceof Error ? emailError.message : String(emailError)}`,
					{ ...context, body: { email: requestedBy } },
				);
				// Don't fail the request if email fails
			}

			const responseData = {
				message: "Refund request submitted successfully",
				refundRequest: {
					id: refundRequest.id,
					status: refundRequest.status,
					reason: refundRequest.reason,
					createdAt: refundRequest.createdAt,
				},
			};

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				"Refund request created successfully",
				{ ...context, body: responseData },
			);

			res.json(responseData);
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_CREATE,
				`Error processing refund request: ${error instanceof Error ? error.message : String(error)}`,
				extractRequestContext(req),
			);

			if (error instanceof z.ZodError) {
				return handleZodError(res, error);
			}

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			return sendErrorResponse(
				res,
				"Failed to process refund request",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while processing the refund request",
			);
		}
	},
);

/**
 * @route POST /api/refunds/process
 * @description Process a refund request (admin endpoint)
 * @access Admin
 *
 * @param {Object} req.body - Request body
 * @param {string} req.body.licenseKey - License key to process refund for
 * @param {string} req.body.action - Action to take (approve/reject)
 * @param {string} [req.body.adminNotes] - Optional admin notes
 * @param {number} [req.body.amount] - Optional amount for partial refunds
 *
 * @returns {Object} 200 - Refund processed successfully
 * @returns {Object} 400 - Validation error or processing failed
 * @returns {Object} 404 - License or refund request not found
 * @returns {Object} 500 - Internal server error
 */
router.post(
	"/process",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_CREATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				"Processing refund approval/rejection",
				context,
			);

			const { licenseKey, action, adminNotes, amount } =
				processRefundSchema.parse(req.body);

			Logger.info(
				EndpointPrefix.LICENSE_CREATE,
				`Processing refund ${action} for license: ${licenseKey}`,
				{
					...context,
					body: { licenseKey, action, amount },
				},
			);

			// Find the license with refund request
			const license = await prisma.license.findUnique({
				where: { licenseKey },
				include: { refundRequest: true },
			});

			if (!license) {
				return sendErrorResponse(
					res,
					"License not found",
					404,
					ErrorCode.LICENSE_NOT_FOUND,
					"The specified license key was not found",
				);
			}

			if (!license.refundRequest) {
				return sendErrorResponse(
					res,
					"No refund request found",
					404,
					ErrorCode.NOT_FOUND,
					"No refund request exists for this license",
				);
			}

			if (license.refundRequest.status !== "PENDING") {
				return sendErrorResponse(
					res,
					"Refund request already processed",
					400,
					ErrorCode.VALIDATION_ERROR,
					"This refund request has already been processed",
					{ currentStatus: license.refundRequest.status },
				);
			}

			if (action === "reject") {
				// Reject the refund request
				await prisma.refundRequest.update({
					where: { licenseId: license.id },
					data: {
						status: "REJECTED",
						processedAt: new Date(),
						adminNotes,
					},
				});

				// Create audit log entry
				await prisma.auditLog.create({
					data: {
						action: "REFUND_REJECTED",
						licenseKey: license.licenseKey,
						details: {
							refundRequestId: license.refundRequest.id,
							adminNotes,
						},
					},
				});

				// Send rejection email
				try {
					await sendRefundRejectionEmail(
						license.refundRequest.requestedBy,
						license.licenseKey,
						license.refundRequest.reason,
						adminNotes,
					);

					Logger.info(
						EndpointPrefix.LICENSE_CREATE,
						`Refund rejection email sent: ${license.refundRequest.requestedBy}`,
						{ ...context, body: { licenseId: license.id } },
					);
				} catch (emailError) {
					Logger.error(
						EndpointPrefix.LICENSE_CREATE,
						`Failed to send refund rejection email: ${emailError instanceof Error ? emailError.message : String(emailError)}`,
						{ ...context, body: { email: license.refundRequest.requestedBy } },
					);
					// Don't fail the request if email fails
				}

				const responseData = {
					message: "Refund request rejected",
					status: "REJECTED",
				};

				Logger.info(EndpointPrefix.LICENSE_CREATE, "Refund request rejected", {
					...context,
					body: responseData,
				});

				return res.json(responseData);
			}

			// Approve and process the refund
			const refundOptions: RefundOptions = {
				licenseId: license.id,
				reason: license.refundRequest.reason,
				requestedBy: license.refundRequest.requestedBy,
				adminNotes,
				amount,
			};

			// Process the refund through Stripe
			const refundResult = amount
				? await processPartialRefund(license, refundOptions)
				: await processFullRefund(license, refundOptions);

			if (!refundResult.success) {
				// Update refund request status to failed
				await prisma.refundRequest.update({
					where: { licenseId: license.id },
					data: {
						status: "FAILED",
						processedAt: new Date(),
						adminNotes: `${adminNotes || ""}\nError: ${refundResult.error}`,
					},
				});

				// Create audit log entry
				await prisma.auditLog.create({
					data: {
						action: "REFUND_FAILED",
						licenseKey: license.licenseKey,
						details: {
							refundRequestId: license.refundRequest.id,
							error: refundResult.error,
							adminNotes,
						},
					},
				});

				return sendErrorResponse(
					res,
					"Refund processing failed",
					400,
					ErrorCode.EXTERNAL_SERVICE_ERROR,
					refundResult.error ||
						"Failed to process refund through payment provider",
				);
			}

			// Update license and database after successful refund
			await updateLicenseAfterRefund(license, refundResult, refundOptions);

			const responseData = {
				message: "Refund processed successfully",
				refund: {
					id: refundResult.refundId,
					amount: refundResult.amount,
					status: "PROCESSED",
				},
			};

			logApiResponse(
				EndpointPrefix.LICENSE_CREATE,
				200,
				responseData,
				context,
				"Refund processed successfully",
			);

			res.json(responseData);
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_CREATE,
				`Error processing refund: ${error instanceof Error ? error.message : String(error)}`,
				extractRequestContext(req),
			);

			if (error instanceof z.ZodError) {
				return handleZodError(res, error);
			}

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			if (error && typeof error === "object" && "type" in error) {
				return handleStripeError(res, error as StripeError);
			}

			return sendErrorResponse(
				res,
				"Failed to process refund",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while processing the refund",
			);
		}
	},
);

/**
 * @route GET /api/refunds/status/:licenseKey
 * @description Get refund status for a license
 * @access Public
 *
 * @param {string} req.params.licenseKey - License key to check status for
 *
 * @returns {Object} 200 - Refund status information
 * @returns {Object} 404 - License not found
 * @returns {Object} 500 - Internal server error
 */
router.get(
	"/status/:licenseKey",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_VALIDATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);
			const { licenseKey } = req.params;

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				`Checking refund status for license: ${licenseKey}`,
				context,
			);

			// Validate license key format
			if (
				!licenseKey ||
				licenseKey.length !== 24 ||
				!/^[A-Z0-9]+$/.test(licenseKey)
			) {
				return sendErrorResponse(
					res,
					"Invalid license key format",
					400,
					ErrorCode.VALIDATION_ERROR,
					"License key must be exactly 24 characters long and contain only uppercase letters and numbers",
				);
			}

			// Find the license with refund information
			const license = await prisma.license.findUnique({
				where: { licenseKey },
				select: {
					id: true,
					licenseKey: true,
					refundedAt: true,
					refundReason: true,
					refundAmount: true,
					stripeRefundId: true,
					refundRequest: {
						select: {
							id: true,
							status: true,
							reason: true,
							createdAt: true,
							processedAt: true,
						},
					},
				},
			});

			if (!license) {
				return sendErrorResponse(
					res,
					"License not found",
					404,
					ErrorCode.LICENSE_NOT_FOUND,
					"The specified license key was not found",
				);
			}

			const responseData = {
				licenseKey: license.licenseKey,
				refunded: !!license.refundedAt,
				refundedAt: license.refundedAt,
				refundReason: license.refundReason,
				refundAmount: license.refundAmount,
				refundRequest: license.refundRequest,
			};

			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				200,
				responseData,
				context,
				"Refund status retrieved successfully",
			);

			res.json(responseData);
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_VALIDATE,
				`Error retrieving refund status: ${error instanceof Error ? error.message : String(error)}`,
				extractRequestContext(req),
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			return sendErrorResponse(
				res,
				"Failed to retrieve refund status",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while retrieving refund status",
			);
		}
	},
);

/**
 * @route GET /api/refunds/history
 * @description Get refund history (admin endpoint)
 * @access Admin
 *
 * @param {number} [req.query.page] - Page number for pagination
 * @param {number} [req.query.limit] - Number of results per page
 * @param {string} [req.query.status] - Filter by refund status
 *
 * @returns {Object} 200 - Refund history data
 * @returns {Object} 500 - Internal server error
 */
router.get(
	"/history",
	requestLoggingMiddleware(EndpointPrefix.LICENSE_VALIDATE),
	async (req, res) => {
		try {
			const context = extractRequestContext(req);

			// Parse and validate query parameters
			const filters = {
				page: Number.parseInt(req.query.page as string) || 1,
				limit: Number.parseInt(req.query.limit as string) || 20,
				status: req.query.status as string,
				search: req.query.search as string,
			};

			// Validate filters using schema
			const validation = listRefundsSchema.safeParse(filters);
			if (!validation.success) {
				return handleZodError(res, validation.error);
			}

			Logger.info(
				EndpointPrefix.LICENSE_VALIDATE,
				"Retrieving refund history",
				{
					...context,
					body: { filters: validation.data },
				},
			);

			const validatedFilters = validation.data;
			const pagination = calculatePagination(validatedFilters);

			// Build where clause
			const where: Prisma.RefundRequestWhereInput = {};

			if (validatedFilters.status) {
				where.status = validatedFilters.status as any;
			}

			if (validatedFilters.search) {
				where.OR = [
					{
						license: {
							licenseKey: { contains: validatedFilters.search, mode: "insensitive" },
						},
					},
					{
						license: {
							email: { contains: validatedFilters.search, mode: "insensitive" },
						},
					},
				];
			}

			// Get refund requests with license information
			const [refundRequests, totalCount] = await Promise.all([
				prisma.refundRequest.findMany({
					where,
					include: {
						license: {
							select: {
								licenseKey: true,
								email: true,
								licenseType: true,
								refundedAt: true,
								refundAmount: true,
								stripeRefundId: true,
							},
						},
					},
					orderBy: { createdAt: "desc" },
					skip: pagination.skip,
					take: pagination.take,
				}),
				prisma.refundRequest.count({ where }),
			]);

			const responseData = {
				refundRequests: refundRequests.map((request) => ({
					id: request.id,
					status: request.status,
					reason: request.reason,
					amount: request.amount,
					requestedBy: request.requestedBy,
					adminNotes: request.adminNotes,
					processedBy: request.processedBy,
					createdAt: request.createdAt,
					processedAt: request.processedAt,
					license: {
						licenseKey: request.license.licenseKey,
						email: request.license.email,
						licenseType: request.license.licenseType,
						refundedAt: request.license.refundedAt,
						refundAmount: request.license.refundAmount,
					},
				})),
				pagination: pagination.meta(totalCount),
			};

			logApiResponse(
				EndpointPrefix.LICENSE_VALIDATE,
				200,
				responseData,
				context,
				"Refund history retrieved successfully",
			);

			res.json(responseData);
		} catch (error) {
			Logger.error(
				EndpointPrefix.LICENSE_VALIDATE,
				`Error retrieving refund history: ${error instanceof Error ? error.message : String(error)}`,
				extractRequestContext(req),
			);

			if (error && typeof error === "object" && "code" in error) {
				return handleDatabaseError(res, error as PrismaError);
			}

			return sendErrorResponse(
				res,
				"Failed to retrieve refund history",
				500,
				ErrorCode.INTERNAL_ERROR,
				"An unexpected error occurred while retrieving refund history",
			);
		}
	},
);

export default router;
