import type { PaginationMeta } from "@snapback/shared";

/**
 * Pagination configuration constants
 */
export const PAGINATION_DEFAULTS = {
	DEFAULT_PAGE: 1,
	DEFAULT_LIMIT: 20,
	MAX_LIMIT: 100,
	MIN_LIMIT: 1,
} as const;

/**
 * Pagination parameters interface
 */
export interface PaginationParams {
	page: number;
	limit: number;
}

/**
 * Pagination calculation result
 */
export interface PaginationCalculation {
	skip: number;
	take: number;
	meta: (totalCount: number) => PaginationMeta;
}

/**
 * Validates and normalizes pagination parameters
 */
export function validatePaginationParams(
	page?: number | string,
	limit?: number | string,
): PaginationParams {
	const normalizedPage = Math.max(
		PAGINATION_DEFAULTS.MIN_LIMIT,
		Number.parseInt(String(page)) || PAGINATION_DEFAULTS.DEFAULT_PAGE,
	);

	const normalizedLimit = Math.min(
		PAGINATION_DEFAULTS.MAX_LIMIT,
		Math.max(
			PAGINATION_DEFAULTS.MIN_LIMIT,
			Number.parseInt(String(limit)) || PAGINATION_DEFAULTS.DEFAULT_LIMIT,
		),
	);

	return {
		page: normalizedPage,
		limit: normalizedLimit,
	};
}

/**
 * Calculates pagination values for database queries
 */
export function calculatePagination(params: PaginationParams): PaginationCalculation {
	const { page, limit } = params;
	const skip = (page - 1) * limit;

	return {
		skip,
		take: limit,
		meta: (totalCount: number): PaginationMeta => {
			const totalPages = Math.ceil(totalCount / limit);
			
			return {
				page,
				limit,
				totalCount,
				totalPages,
				hasNextPage: page < totalPages,
				hasPreviousPage: page > 1,
			};
		},
	};
}

/**
 * Creates a standardized paginated response
 */
export function createPaginatedResponse<T>(
	data: T[],
	totalCount: number,
	params: PaginationParams,
): { data: T[]; pagination: PaginationMeta } {
	const pagination = calculatePagination(params);
	
	return {
		data,
		pagination: pagination.meta(totalCount),
	};
}

/**
 * Extracts pagination parameters from Express request query
 */
export function extractPaginationFromQuery(query: {
	page?: string;
	limit?: string;
}): PaginationParams {
	return validatePaginationParams(query.page, query.limit);
}
